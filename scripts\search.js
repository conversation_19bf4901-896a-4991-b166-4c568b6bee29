// Search functionality for music app

document.addEventListener("DOMContentLoaded", () => {
  // Preserve any ongoing playback before loading new state
  if (window.GlobalAudioManager) {
    const playbackState = window.GlobalAudioManager.preservePlayback();
    console.log("Preserved playback state on search page:", playbackState);

    // If music was playing, ensure it continues
    if (playbackState.wasPlaying) {
      console.log("Music was playing - ensuring continuity on search page");
    }
  }

  // Load player state first - this will now respect ongoing playback
  if (typeof loadPlayerState === "function") {
    loadPlayerState();
  }

  // Initialize search functionality
  initializeSearch();
});

function initializeSearch() {
  // Get search elements
  const search_result = document.querySelector(".search_result");
  const search_input = document.querySelector(".search input");

  // If search elements don't exist, return
  if (!search_result || !search_input) return;

  // Clear existing search results
  search_result.innerHTML = "";

  // Populate search results with songs
  songs.forEach((element) => {
    const { id, songName, poster } = element;
    let card = document.createElement("div");
    card.classList.add("card");
    card.innerHTML = `
    <img src="${poster}" alt="">
    <div class="content">
       ${songName}
    </div>
    <div class="card-actions">
      <i class="bi playListPlay bi-play-circle-fill" id="${id}" title="Play"></i>
      <i class="bi bi-heart add-to-library search-heart" data-id="${id}" title="Add to Library"></i>
    </div>
    `;
    search_result.appendChild(card);
  });

  // Initialize heart icons for search results
  initializeSearchHeartIcons();

  // Add event listeners to search result play buttons
  addSearchPlayButtonListeners();

  // Add search input event listener
  search_input.addEventListener("input", () => {
    let search_value = search_input.value.toLowerCase();

    // Show search results container when there's input
    if (search_value.length > 0) {
      search_result.style.display = "flex";
    } else {
      search_result.style.display = "none";
      return;
    }

    // Filter search results
    Array.from(search_result.getElementsByClassName("card")).forEach((card) => {
      let content = card
        .getElementsByClassName("content")[0]
        .innerText.toLowerCase();
      if (content.includes(search_value)) {
        card.style.display = "flex";
      } else {
        card.style.display = "none";
      }
    });
  });

  // Hide search results initially
  search_result.style.display = "none";

  // Add click event to search result cards for navigation
  Array.from(search_result.getElementsByClassName("card")).forEach((card) => {
    card.addEventListener("click", (e) => {
      // Only navigate if the click is not on the play button or heart icon
      if (
        !e.target.classList.contains("playListPlay") &&
        !e.target.classList.contains("add-to-library")
      ) {
        const playButton = card.querySelector(".playListPlay");
        const songId = playButton ? playButton.id : null;

        if (songId) {
          window.location.href = `music-detail.html?id=${songId}&source=search`;
        }
      }
    });
  });

  // Listen for cross-page play/pause synchronization events
  window.addEventListener("playPauseSync", (event) => {
    const { songId, isPlaying } = event.detail;

    // Update all music card play buttons on this page
    const allPlayButtons = document.querySelectorAll(".playListPlay");
    allPlayButtons.forEach((button) => {
      if (button.id === songId && isPlaying) {
        button.classList.remove("bi-play-circle-fill");
        button.classList.add("bi-pause-circle-fill");
      } else {
        button.classList.add("bi-play-circle-fill");
        button.classList.remove("bi-pause-circle-fill");
      }
    });
  });
}

// Initialize heart icons for search results
function initializeSearchHeartIcons() {
  // Set initial state for search heart icons
  refreshSearchHeartIcons();
}

// Refresh search heart icons (called when returning to pages)
function refreshSearchHeartIcons() {
  document.querySelectorAll(".search-heart").forEach((heartIcon) => {
    const songId = heartIcon.getAttribute("data-id");
    if (songId && typeof isSongInLibrary === "function") {
      if (isSongInLibrary(songId)) {
        heartIcon.classList.remove("bi-heart");
        heartIcon.classList.add("bi-heart-fill", "in-library");
        heartIcon.title = "Remove from Library";
      } else {
        heartIcon.classList.remove("bi-heart-fill", "in-library");
        heartIcon.classList.add("bi-heart");
        heartIcon.title = "Add to Library";
      }
    }
  });
}

// Make refresh function globally available
window.refreshSearchHeartIcons = refreshSearchHeartIcons;

// Add click event listeners to search heart icons
function addSearchHeartIconListeners() {
  document.querySelectorAll(".search-heart").forEach((heartIcon) => {
    heartIcon.addEventListener("click", (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Check if user is logged in
      if (typeof isUserLoggedIn === "function" && !isUserLoggedIn()) {
        if (typeof showLoginRequiredNotification === "function") {
          showLoginRequiredNotification();
        }
        return;
      }

      const songId = e.target.getAttribute("data-id");
      if (!songId) return;

      // Check if library functions are available
      if (
        typeof isSongInLibrary !== "function" ||
        typeof addSongToLibrary !== "function" ||
        typeof removeSongFromLibrary !== "function"
      ) {
        console.warn("Library functions not available");
        return;
      }

      if (isSongInLibrary(songId)) {
        // Remove from library
        if (removeSongFromLibrary(songId)) {
          // Sync all heart icons
          if (typeof syncAllHeartIcons === "function") {
            syncAllHeartIcons(songId);
          }
        }
      } else {
        // Add to library
        if (addSongToLibrary(songId)) {
          // Sync all heart icons
          if (typeof syncAllHeartIcons === "function") {
            syncAllHeartIcons(songId);
          }
        }
      }
    });
  });
}

// Add event listeners to search result play buttons
function addSearchPlayButtonListeners() {
  document
    .querySelectorAll(".search_result .playListPlay")
    .forEach((playButton) => {
      playButton.addEventListener("click", (e) => {
        e.preventDefault();
        e.stopPropagation();

        const songId = e.target.id;

        // Use the centralized play function for bidirectional sync
        if (typeof window.handleMusicCardPlayClick === "function") {
          window.handleMusicCardPlayClick(songId, e);
        }
      });
    });
}

// Initialize search heart icon listeners
addSearchHeartIconListeners();
