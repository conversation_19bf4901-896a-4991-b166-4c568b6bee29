<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Thư <PERSON>n <PERSON> Tôi - Music App</title>
    <link rel="stylesheet" href="./styles/common.css" />
    <link rel="stylesheet" href="./styles/index.css" />
    <link rel="stylesheet" href="./styles/library.css" />
    <link rel="stylesheet" href="./styles/notification.css" />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />
  </head>
  <body>
    <header>
      <div class="menu_side custom-scrollbar">
        <h1>Music App</h1>
        <div class="playlist">
          <h4>
            <a href="index.html" class="active"
              ><span></span><i class="bi bi-music-note-beamed"></i> <PERSON>h<PERSON><PERSON></a
            >
          </h4>
          <h4 class="active">
            <span></span><i class="bi bi-music-note-beamed"></i> Thư Viện Của
            Tôi
          </h4>
          <h4>
            <a href="recently-played.html" class="active">
              <span></span><i class="bi bi-music-note-beamed"></i> Gần Đây</a
            >
          </h4>
        </div>
        <div class="auth-links">
          <h4>
            <a href="login.html"
              ><i class="bi bi-box-arrow-in-right"></i> Đăng Nhập</a
            >
          </h4>
          <h4>
            <a href="register.html"
              ><i class="bi bi-person-plus"></i> Đăng Ký</a
            >
          </h4>
        </div>
        <div class="menu_song">
          <!-- Song list will be populated by JavaScript -->
        </div>
      </div>
      <div class="song_side">
        <nav>
          <ul>
            <li><a href="index.html">Khám Phá</a></li>
            <li class="active">Thư Viện Của Tôi<span></span></li>
            <li><a href="recently-played.html">Gần Đây</a></li>
          </ul>
          <div class="search">
            <i class="bi bi-search"></i>
            <input type="text" placeholder="Tìm kiếm trong thư viện..." />
            <div class="search_result custom-scrollbar">
              <!-- Search results will be populated by JavaScript -->
            </div>
          </div>
          <div class="user">
            <img
              src="./styles/images/avatar-trang-4.jpg"
              alt="User"
              title="Guest"
            />
          </div>
        </nav>

        <div class="library-container custom-scrollbar">
          <div class="library-header">
            <h1>Thư Viện Của Tôi</h1>
            <p>Tất cả bài hát bạn đã lưu sẽ xuất hiện ở đây</p>
          </div>

          <div class="library-content">
            <div class="empty-library" id="empty-library">
              <i class="bi bi-music-note-list"></i>
              <h2>Thư viện của bạn đang trống</h2>
              <p>
                Thêm bài hát vào thư viện bằng cách nhấn vào biểu tượng "Thêm
                vào thư viện" khi nghe nhạc
              </p>
              <a href="index.html" class="action-btn">
                <i class="bi bi-music-note-beamed"></i> Khám phá nhạc
              </a>
            </div>

            <div class="library-songs" id="library-songs">
              <!-- Library songs will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>

      <div class="master_play">
        <div class="wave">
          <div class="wave1"></div>
          <div class="wave1"></div>
          <div class="wave1"></div>
        </div>
        <img
          id="poster_master_play"
          src="./styles/images/img/1.jpg"
          alt="Alan"
        />
        <h5 id="title">
          On My Way<br />
          <div class="subtitle">Alan Walker</div>
        </h5>
        <div class="icon">
          <i class="bi shuffle bi-music-note-beamed">next</i>
          <i class="bi bi-skip-start-fill" id="back"></i>
          <i class="bi bi-play-fill" id="masterPlay"></i>
          <i class="bi bi-skip-end-fill" id="next"></i>
          <i
            class="bi bi-heart master-heart"
            id="masterHeart"
            title="Add to Library"
          ></i>
          <a href="" download id="download_music"
            ><i class="bi bi-cloud-arrow-down-fill"></i
          ></a>
        </div>
        <span id="currentStart">0:00</span>
        <div class="bar">
          <input type="range" id="seek" min="0" value="0" max="100" />
          <div class="bar2" id="bar2"></div>
          <div class="dot"></div>
        </div>
        <span id="currentEnd">0:00</span>

        <div class="vol">
          <i class="bi bi-volume-down-fill" id="vol_icon"></i>
          <input type="range" id="vol" min="0" value="30" max="100" />
          <div class="vol_bar"></div>
          <div class="dot" id="vol_dot"></div>
        </div>
      </div>
    </header>

    <script src="./scripts/user-data-manager.js"></script>
    <script src="./scripts/auth.js"></script>
    <script src="./scripts/notification.js"></script>
    <script src="./scripts/playback-preservation.js"></script>
    <script src="./scripts/master-player-context.js"></script>
    <script src="./scripts/index.js"></script>
    <script src="./scripts/library.js"></script>
    <script src="./scripts/search.js"></script>
  </body>
</html>
