// User-Specific Data Management System
// This module handles user-specific data persistence and isolation

// Get current user ID
function getCurrentUserId() {
  const currentUser = JSON.parse(localStorage.getItem("currentUser"));
  return currentUser ? currentUser.id : null;
}

// Generate user-specific localStorage key
function getUserSpecificKey(baseKey) {
  const userId = getCurrentUserId();
  if (!userId) {
    // If no user is logged in, use global keys (for backward compatibility)
    return baseKey;
  }
  return `user_${userId}_${baseKey}`;
}

// User-specific localStorage operations
const UserDataManager = {
  // Get user-specific data
  getUserData: function(key, defaultValue = null) {
    const userKey = getUserSpecificKey(key);
    const data = localStorage.getItem(userKey);
    return data ? JSON.parse(data) : defaultValue;
  },

  // Set user-specific data
  setUserData: function(key, value) {
    const userKey = getUserSpecificKey(key);
    localStorage.setItem(userKey, JSON.stringify(value));
  },

  // Remove user-specific data
  removeUserData: function(key) {
    const userKey = getUserSpecificKey(key);
    localStorage.removeItem(userKey);
  },

  // Check if user-specific data exists
  hasUserData: function(key) {
    const userKey = getUserSpecificKey(key);
    return localStorage.getItem(userKey) !== null;
  },

  // Get all user-specific keys for current user
  getUserKeys: function() {
    const userId = getCurrentUserId();
    if (!userId) return [];
    
    const userPrefix = `user_${userId}_`;
    const keys = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(userPrefix)) {
        keys.push(key);
      }
    }
    
    return keys;
  },

  // Clear all data for current user
  clearCurrentUserData: function() {
    const userKeys = this.getUserKeys();
    userKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log(`Cleared ${userKeys.length} user-specific data entries`);
  },

  // Migrate existing global data to user-specific data (for first-time login)
  migrateGlobalDataToUser: function() {
    const userId = getCurrentUserId();
    if (!userId) return;

    const globalKeys = ['librarySongs', 'recentlyPlayedSongs', 'currentPlayerState'];
    let migrated = false;

    globalKeys.forEach(key => {
      const globalData = localStorage.getItem(key);
      const userKey = getUserSpecificKey(key);
      
      // Only migrate if global data exists and user-specific data doesn't exist
      if (globalData && !localStorage.getItem(userKey)) {
        localStorage.setItem(userKey, globalData);
        migrated = true;
        console.log(`Migrated ${key} to user-specific storage`);
      }
    });

    // Clear global data after migration to prevent conflicts
    if (migrated) {
      globalKeys.forEach(key => {
        localStorage.removeItem(key);
      });
      console.log('Cleared global data after migration');
    }
  },

  // Initialize user data on login
  initializeUserData: function() {
    const userId = getCurrentUserId();
    if (!userId) return;

    console.log(`Initializing data for user ${userId}`);
    
    // Migrate any existing global data
    this.migrateGlobalDataToUser();
    
    // Initialize empty data structures if they don't exist
    if (!this.hasUserData('librarySongs')) {
      this.setUserData('librarySongs', []);
    }
    
    if (!this.hasUserData('recentlyPlayedSongs')) {
      this.setUserData('recentlyPlayedSongs', []);
    }
    
    console.log('User data initialization complete');
  }
};

// Global function to clear user data (called from auth.js)
function clearUserData() {
  UserDataManager.clearCurrentUserData();
  
  // Also clear any remaining global data
  const globalKeys = ['librarySongs', 'recentlyPlayedSongs', 'currentPlayerState'];
  globalKeys.forEach(key => {
    localStorage.removeItem(key);
  });
  
  console.log('All user data cleared on logout');
}

// Global function to initialize user data on login
function initializeUserData() {
  UserDataManager.initializeUserData();
}

// Export for use in other modules
if (typeof window !== 'undefined') {
  window.UserDataManager = UserDataManager;
  window.getCurrentUserId = getCurrentUserId;
  window.getUserSpecificKey = getUserSpecificKey;
  window.clearUserData = clearUserData;
  window.initializeUserData = initializeUserData;
}
