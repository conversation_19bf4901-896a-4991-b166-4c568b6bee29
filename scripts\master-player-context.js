// Master Player Context Management System
// This system provides page-specific master player functionality

// Global context management
window.MasterPlayerContext = {
  currentContext: "index", // default context
  contexts: {
    index: {
      name: "index",
      tracks: [],
      currentIndex: -1,
      getAvailableTracks: function () {
        return songs || [];
      },
    },
    library: {
      name: "library",
      tracks: [],
      currentIndex: -1,
      getAvailableTracks: function () {
        const librarySongIds = getLibrarySongs ? getLibrarySongs() : [];
        return (songs || []).filter((song) => librarySongIds.includes(song.id));
      },
    },
    "recently-played": {
      name: "recently-played",
      tracks: [],
      currentIndex: -1,
      getAvailableTracks: function () {
        const recentlyPlayedEntries = getRecentlyPlayedSongs
          ? getRecentlyPlayedSongs()
          : [];
        return recentlyPlayedEntries
          .map((entry) =>
            (songs || []).find((song) => song.id === entry.songId)
          )
          .filter((song) => song !== null && song !== undefined);
      },
    },
    search: {
      name: "search",
      tracks: [],
      currentIndex: -1,
      getAvailableTracks: function () {
        return songs || [];
      },
    },
    "music-detail": {
      name: "music-detail",
      tracks: [],
      currentIndex: -1,
      getAvailableTracks: function () {
        return songs || [];
      },
    },
  },

  // Initialize context system
  init: function () {
    this.detectCurrentContext();
    this.updateContextTracks();
    this.setupGlobalEventListeners();
    this.overrideMasterPlayerControls();
  },

  // Detect current page context based on URL
  detectCurrentContext: function () {
    const pathname = window.location.pathname;

    if (pathname.includes("library.html")) {
      this.currentContext = "library";
    } else if (pathname.includes("recently-played.html")) {
      this.currentContext = "recently-played";
    } else if (pathname.includes("search.html")) {
      this.currentContext = "search";
    } else if (pathname.includes("music-detail.html")) {
      this.currentContext = "music-detail";
    } else {
      this.currentContext = "index";
    }

    console.log("Master Player Context:", this.currentContext);
  },

  // Update tracks for current context
  updateContextTracks: function () {
    const context = this.contexts[this.currentContext];
    if (context) {
      context.tracks = context.getAvailableTracks();

      // Update current index if a song is playing
      if (typeof index !== "undefined" && index) {
        context.currentIndex = context.tracks.findIndex(
          (song) => song.id === index.toString()
        );
      }
    }
  },

  // Get current context
  getCurrentContext: function () {
    return this.contexts[this.currentContext];
  },

  // Get next song in current context
  getNextSong: function () {
    const context = this.getCurrentContext();
    if (!context || context.tracks.length === 0) return null;

    context.currentIndex = (context.currentIndex + 1) % context.tracks.length;
    return context.tracks[context.currentIndex];
  },

  // Get previous song in current context
  getPreviousSong: function () {
    const context = this.getCurrentContext();
    if (!context || context.tracks.length === 0) return null;

    context.currentIndex =
      context.currentIndex <= 0
        ? context.tracks.length - 1
        : context.currentIndex - 1;
    return context.tracks[context.currentIndex];
  },

  // Get random song in current context
  getRandomSong: function () {
    const context = this.getCurrentContext();
    if (!context || context.tracks.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * context.tracks.length);
    context.currentIndex = randomIndex;
    return context.tracks[randomIndex];
  },

  // Get current song (for repeat)
  getCurrentSong: function () {
    const context = this.getCurrentContext();
    if (!context || context.tracks.length === 0 || context.currentIndex < 0)
      return null;

    return context.tracks[context.currentIndex];
  },

  // Set current song in context
  setCurrentSong: function (songId) {
    const context = this.getCurrentContext();
    if (!context) return;

    // Update tracks first
    this.updateContextTracks();

    // Find and set current index
    context.currentIndex = context.tracks.findIndex(
      (song) => song.id === songId
    );
  },

  // Setup global event listeners
  setupGlobalEventListeners: function () {
    // Listen for page navigation
    window.addEventListener("beforeunload", () => {
      this.cleanup();
    });

    // Listen for popstate (back/forward navigation)
    window.addEventListener("popstate", () => {
      setTimeout(() => {
        this.detectCurrentContext();
        this.updateContextTracks();
        this.overrideMasterPlayerControls();
      }, 100);
    });
  },

  // Override master player controls for context-specific behavior
  overrideMasterPlayerControls: function () {
    // Override next button
    const nextButton = document.getElementById("next");
    if (nextButton) {
      // Remove existing listeners by cloning
      const newNextButton = nextButton.cloneNode(true);
      nextButton.parentNode.replaceChild(newNextButton, nextButton);

      newNextButton.addEventListener("click", () => {
        this.contextualNext();
      });
    }

    // Override back button
    const backButton = document.getElementById("back");
    if (backButton) {
      // Remove existing listeners by cloning
      const newBackButton = backButton.cloneNode(true);
      backButton.parentNode.replaceChild(newBackButton, backButton);

      newBackButton.addEventListener("click", () => {
        this.contextualPrevious();
      });
    }

    // Override music ended event
    if (typeof music !== "undefined" && music) {
      // Remove existing ended listeners
      music.removeEventListener("ended", this.handleMusicEnded);
      music.addEventListener("ended", this.handleMusicEnded.bind(this));
    }
  },

  // Contextual next function
  contextualNext: function () {
    const nextSong = this.getNextSong();
    if (nextSong) {
      this.playContextualSong(nextSong);
    }
  },

  // Contextual previous function
  contextualPrevious: function () {
    const previousSong = this.getPreviousSong();
    if (previousSong) {
      this.playContextualSong(previousSong);
    }
  },

  // Contextual repeat function
  contextualRepeat: function () {
    const currentSong = this.getCurrentSong();
    if (currentSong) {
      this.playContextualSong(currentSong);
    }
  },

  // Contextual random function
  contextualRandom: function () {
    const randomSong = this.getRandomSong();
    if (randomSong) {
      this.playContextualSong(randomSong);
    }
  },

  // Handle music ended event
  handleMusicEnded: function () {
    const shuffleButton = document.getElementsByClassName("shuffle")[0];
    const shuffleMode = shuffleButton ? shuffleButton.innerHTML : "next";

    switch (shuffleMode) {
      case "repeat":
        this.contextualRepeat();
        break;
      case "next":
        this.contextualNext();
        break;
      case "random":
        this.contextualRandom();
        break;
      default:
        this.contextualNext();
        break;
    }
  },

  // Play a song with full context awareness
  playContextualSong: function (song) {
    if (!song) return;

    // Update current index in context
    this.setCurrentSong(song.id);

    // Set global index for compatibility
    if (typeof window.index !== "undefined") {
      window.index = parseInt(song.id);
    }

    // Get the global audio instance to ensure continuity
    const music = window.GlobalAudioManager
      ? window.GlobalAudioManager.getInstance()
      : typeof window.music !== "undefined"
      ? window.music
      : null;

    if (!music) return;

    // Update music player source
    music.src = `./audio/${song.id}.mp3`;

    // Update master player UI
    const poster_master_play = document.getElementById("poster_master_play");
    if (poster_master_play) {
      poster_master_play.src = `./styles/images/img/${song.id}.jpg`;
    }

    const title = document.getElementById("title");
    if (title) {
      title.innerHTML = song.songName;
    }

    const download_music = document.getElementById("download_music");
    if (download_music) {
      download_music.href = `./audio/${song.id}.mp3`;
      if (typeof extractSongName === "function") {
        download_music.setAttribute("download", extractSongName(song.songName));
      }
    }

    // Play the song with error handling
    music
      .play()
      .then(() => {
        console.log("Song started playing successfully");
      })
      .catch((error) => {
        console.log("Auto-play prevented or error:", error);
      });

    // Update UI elements
    const masterPlay = document.getElementById("masterPlay");
    const wave = document.querySelector(".wave");

    if (masterPlay) {
      masterPlay.classList.remove("bi-play-fill");
      masterPlay.classList.add("bi-pause-fill");
    }

    if (wave) {
      wave.classList.add("active2");
    }

    // Update play buttons
    if (typeof makeAllPlays === "function") {
      makeAllPlays();
    }

    // Update current song highlight
    const playButton = document.getElementById(song.id);
    if (playButton) {
      playButton.classList.remove("bi-play-circle-fill");
      playButton.classList.add("bi-pause-circle-fill");
    }

    // Save player state
    if (typeof saveCurrentPlayerState === "function") {
      saveCurrentPlayerState(song.id, 0, true);
    }

    // Add to recently played
    if (typeof addSongToRecentlyPlayed === "function") {
      addSongToRecentlyPlayed(song.id);
    }

    // Update heart icon
    if (typeof updateMasterHeartIcon === "function") {
      updateMasterHeartIcon();
    }

    // Update backgrounds for index page
    if (this.currentContext === "index") {
      if (typeof makeAllBackgrounds === "function") {
        makeAllBackgrounds();
      }

      const songItems = document.getElementsByClassName("songItem");
      if (songItems && songItems[parseInt(song.id) - 1]) {
        songItems[parseInt(song.id) - 1].style.background =
          "rgb(105, 105, 170, .1)";
      }
    }
  },

  // Cleanup function
  cleanup: function () {
    // Remove event listeners if needed
    if (typeof music !== "undefined" && music) {
      music.removeEventListener("ended", this.handleMusicEnded);
    }
  },

  // Refresh context (useful when library or recently played changes)
  refreshContext: function () {
    this.updateContextTracks();
  },
};

// Initialize context system when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  // Wait a bit for other scripts to load
  setTimeout(() => {
    if (window.MasterPlayerContext) {
      window.MasterPlayerContext.init();
    }
  }, 100);
});

// Export for use in other scripts
window.refreshMasterPlayerContext = function () {
  if (window.MasterPlayerContext) {
    window.MasterPlayerContext.refreshContext();
  }
};
