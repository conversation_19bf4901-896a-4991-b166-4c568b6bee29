// <PERSON><PERSON><PERSON> tra trạng thái đăng nhập
const checkAuthState = () => {
  const currentUser = JSON.parse(localStorage.getItem("currentUser"));
  const authLinks = document.querySelector(".auth-links");
  const userInfo = document.querySelector(".user");

  if (currentUser) {
    // Nếu đã đăng nhập
    if (authLinks) {
      authLinks.innerHTML = `
                <h4>
                    <a href="#"><i class="bi bi-person"></i> ${currentUser.email}</a>
                </h4>
                <h4>
                    <a href="#" onclick="handleLogout()"><i class="bi bi-box-arrow-right"></i> Đăng xuất</a>
                </h4>
            `;
    }

    // Cập nhật ảnh người dùng nếu có
    if (userInfo) {
      userInfo.innerHTML = `
                <img src="./styles/images/img/user.jpg" alt="User" title="${currentUser.email}"/>
            `;
    }

    return true;
  } else {
    // Nếu chưa đăng nhập
    if (authLinks) {
      authLinks.innerHTML = `
                <h4>
                    <a href="login.html"><i class="bi bi-box-arrow-in-right"></i> Đăng nhập</a>
                </h4>
                <h4>
                    <a href="register.html"><i class="bi bi-person-plus"></i> Đăng ký</a>
                </h4>
            `;
    }

    // Reset ảnh người dùng mặc định
    if (userInfo) {
      userInfo.innerHTML = `
                <img src="./styles/images/profile.png" alt="User" title="Guest"/>
            `;
    }

    return false;
  }
};

// Kiểm tra xem người dùng có đăng nhập hay không (utility function)
const isUserLoggedIn = () => {
  const currentUser = localStorage.getItem("currentUser");
  return currentUser !== null && currentUser !== undefined;
};

// Hiển thị thông báo yêu cầu đăng nhập
const showLoginRequiredNotification = () => {
  if (typeof showNotification === "function") {
    showNotification("Vui lòng đăng nhập tài khoản!", 3000);
  } else {
    // Fallback alert if notification system is not available
    alert("Vui lòng đăng nhập tài khoản!");
  }
};

// Global function to initialize download authentication for master player
const initializeGlobalDownloadAuthentication = () => {
  // Add authentication check to master player download link
  const downloadMusic = document.getElementById("download_music");
  if (downloadMusic) {
    // Remove any existing event listeners to avoid duplicates
    const newDownloadMusic = downloadMusic.cloneNode(true);
    downloadMusic.parentNode.replaceChild(newDownloadMusic, downloadMusic);

    newDownloadMusic.addEventListener("click", (e) => {
      // Check if user is logged in
      if (!isUserLoggedIn()) {
        e.preventDefault(); // Prevent download
        showLoginRequiredNotification();
        return false;
      }
      // If logged in, allow download to proceed normally
    });
  }
};

// Xử lý đăng xuất
const handleLogout = () => {
  // Clear all user-specific data before logout
  if (typeof clearUserData === "function") {
    clearUserData();
  }

  // Xóa thông tin người dùng hiện tại
  localStorage.removeItem("currentUser");

  // Cập nhật lại giao diện
  checkAuthState();

  // Chuyển về trang chủ
  window.location.href = "index.html";
};

// Kiểm tra trạng thái auth khi trang được tải
document.addEventListener("DOMContentLoaded", () => {
  checkAuthState();
  // Initialize download authentication for master player on all pages
  initializeGlobalDownloadAuthentication();
});
